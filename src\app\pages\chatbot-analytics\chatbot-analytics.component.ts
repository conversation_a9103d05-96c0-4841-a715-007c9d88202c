import {
  Component,
  OnInit,
  On<PERSON><PERSON>roy,
  AfterViewInit,
  ChangeDetectorRef,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Subject } from 'rxjs';
import { ChatAnalyticsService } from './services/chat-analytics.service';
import { UsersService } from 'src/app/pages/organizations/services/users.service';
import { MisuseDetectionService } from './services/misuse-detection.service';
import {
  Conversation,
  ChatAnalyticsKPIs,
  DateRange,
  ErrorDetails,
  ChatType,
  ChatTypeOption,
  CHAT_TYPE_OPTIONS,
  ChatTypeUtils
} from './models/chat-analytics.model';
import {
  formatDateForDisplay,
  formatDateForInput,
  formatDateForApi,
  getMessageTime,
} from './utils/date.utils';
import { PaginationUtils} from './utils/pagination.utils';
import { UserUtils } from './utils/user.utils';
import { SortUtils, SortConfig, SortDirection, SortField } from './utils/sort.utils';

type AnalyticsDateRange = '24h' | '7d' | '30d' | '90d' | 'custom';
type TrendMetric = 'volume' | 'duration' | 'satisfaction';

@Component({
  selector: 'app-chatbot-analytics',
  templateUrl: './chatbot-analytics.component.html',
  styleUrls: []
})
export class ChatbotAnalyticsComponent implements OnInit, OnDestroy, AfterViewInit {
  private destroy$ = new Subject<void>();
  error: ErrorDetails | null = null;
  loading = false;
  usersLoading = false;
  isFormInitialized = false;
  isDataLoaded = false;
  lastUpdated: Date | null = null;
  showFilterPanel = true;

  currentTab: 'conversations' | 'analytics' | 'userChatbot' = 'conversations';
  analyticsDateRange: AnalyticsDateRange = '7d';
  trendMetric: TrendMetric = 'volume';
  searchQuery = '';
  sortBy = 'Most Recent';
  sortOrder: 'asc' | 'desc' = 'desc';
  selectedChatType = '';
  selectedUser: string | null = null;
  dateRange: DateRange = { start: null, end: null };
  selectedChatTypes: string[] = [];

  volumeChartData: any[] = [];
  volumeChartCategories: string[] = [];
  volumeChartSeries: ApexAxisChartSeries = [{
    name: 'Conversations',
    data: []
  }];
  volumeChartHeight = 300;
  volumeChartColors: string[] = ['#7239EA'];

  donutChartSeries: number[] = [];
  donutChartLabels: string[] = [];
  donutChartColors: string[] = [];

  filterForm: FormGroup;

  users: any[] = [];
  conversations: any[] = [];
  allConversations: any[] = [];

  sortOptions = [
    { value: 'Most Recent', label: 'Most Recent' },
    { value: 'Oldest', label: 'Oldest' },
    { value: 'User Name', label: 'User Name' }
  ];

  chatTypeOptions: ChatTypeOption[] = CHAT_TYPE_OPTIONS.filter(option => option.value !== '');

  dateRangeOptions = [
    { value: '24h', label: 'Last 24 hours' },
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: 'custom', label: 'Custom Range' }
  ];

  constructor(
    private readonly chatAnalyticsService: ChatAnalyticsService,
    private readonly usersService: UsersService,
    private readonly fb: FormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly misuseDetectionService: MisuseDetectionService
  ) {
    this.initializeForm();
    this.selectedChatType = '';
  }

  async ngOnInit(): Promise<void> {
    this.initializeDateRange();
    this.setDefaultFilters();
    this.initializeChartColors();
    this.loadUsers();
    this.loadConversations();
    try {
      await this.loadConversations();
      if (this.currentTab === 'analytics') {
        setTimeout(() => {
          this.initCharts();
        }, 100);
      }
    } catch (error) {
      console.error('Error initializing component:', error);
    }
  }

  private initializeChartColors(): void {
    this.donutChartColors = Object.values(ChatType).map(type =>
      ChatTypeUtils.getChartColor(type)
    );
  }

  ngAfterViewInit(): void {
    if (this.currentTab === 'analytics') {
      this.initCharts();
    }
  }

  private initializeForm(): void {
    try {
      const formGroup = this.fb.group({
        searchStartDate: [null],
        searchEndDate: [null],
        chatType: [''],
        search: [''],
        userId: ['']
      });

      this.filterForm = formGroup;

      this.isFormInitialized = true;

      this.initializeDateRange();

      this.loadUsers();
    } catch (error) {
      console.error('Error initializing form:', error);
      this.filterForm = this.fb.group({});
      this.isFormInitialized = true;
    }
  }

  private setDefaultFilters(): void {
    if (this.filterForm) {
      this.filterForm.patchValue({
        searchStartDate: null,
        searchEndDate: null,
        chatType: '',
        search: '',
        userId: ''
      });
    }
  }

  private initializeDateRange(): void {
    this.dateRange = { start: null, end: null };
    if (this.filterForm) {
      this.filterForm.patchValue({
        searchStartDate: null,
        searchEndDate: null
      });
    }
  }

  public loadUsers(): void {
    this.usersLoading = true;
    this.usersService.getExistingUsers().subscribe({
      next: (response) => {
        if (response?.data?.userByDate?.items) {
          this.users = response.data.userByDate.items.map((user: any) => ({
            id: user.id,
            name: (user.name ?? user.email) ?? 'Unknown User',
            email: user.email ?? ''
          }));
        } else {
          this.users = [];
        }
        this.usersLoading = false;
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.users = [];
        this.usersLoading = false;
      }
    });
  }

  private processChatTypeData(conversations: any[]): { series: number[]; labels: string[] } {
    const typeCounts = new Map<ChatType, number>();

    Object.values(ChatType).forEach(type => {
      typeCounts.set(type, 0);
    });

    conversations.forEach(conv => {
      if (conv.chatType && Object.values(ChatType).includes(conv.chatType)) {
        const count = typeCounts.get(conv.chatType as ChatType) || 0;
        typeCounts.set(conv.chatType as ChatType, count + 1);
      }
    });

    const labels: string[] = [];
    const series: number[] = [];

    const sortedTypes = Array.from(typeCounts.entries())
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count);

    sortedTypes.forEach(({ type, count }) => {
      labels.push(type);
      series.push(count);
    });

    if (series.every(count => count === 0)) {
      return { series: [], labels: [] };
    }

    return { series, labels };
  }

  private getChatTypeColors(labels: string[]): string[] {
    return ChatTypeUtils.getChartColors(labels);
  }

  private updateKPIs(): void {
    try {
      const filteredConversations = this.allConversations.filter(conv => {
        if (this.selectedChatType && conv.chatType !== this.selectedChatType) {
          return false;
        }

        if (this.searchQuery) {
          const searchLower = this.searchQuery.toLowerCase();
          const matchesSearch = conv.messages?.some(msg =>
            msg.content?.toLowerCase().includes(searchLower)
          );
          if (!matchesSearch) return false;
        }

        if (this.dateRange.start || this.dateRange.end) {
          const convDate = new Date(conv.timestamp).getTime();
          const startDate = this.dateRange.start ? new Date(this.dateRange.start).setHours(0, 0, 0, 0) : -Infinity;
          const endDate = this.dateRange.end ? new Date(this.dateRange.end).setHours(23, 59, 59, 999) : Infinity;

          if (convDate < startDate || convDate > endDate) return false;
        }

        return true;
      });

      const uniqueUsers = new Set(filteredConversations.map(conv => conv.userId));
      
      const preserveCurrentFlaggedUsers = this.kpis.flaggedUsers;

      this.kpis = {
        totalConversations: filteredConversations.length,
        uniqueUsers: uniqueUsers.size,
        flaggedUsers: preserveCurrentFlaggedUsers
      };
    } catch (error) {
      console.error('Error updating KPIs:', error);
    }
  }

  onPageChange(page: number): void {
    const paginationInfo = PaginationUtils.getPaginationInfo({
      currentPage: this.currentPage,
      itemsPerPage: this.ITEMS_PER_PAGE,
      totalItems: this.totalItems
    });
    
    if (!PaginationUtils.isValidPage(page, paginationInfo.totalPages) || page === this.currentPage) {
      return;
    }

    this.currentPage = page;
    this.updateDisplayedConversations();
    PaginationUtils.scrollToElement();
  }

  getChatTypeLabel(type: string): string {
    return ChatTypeUtils.getLabel(type);
  }

  formattedConversations: any[] = [];
  kpis: ChatAnalyticsKPIs = {
    totalConversations: 0,
    uniqueUsers: 0,
    flaggedUsers: 0,
    totalMessages: 0,
    avgResponseTime: 0,
    satisfactionRate: 0,
    successRate: 0
  };

  currentPage = 1;

  refreshAnimation = false;
  ITEMS_PER_PAGE = 10;
  totalItems = 0;
  showPagination = true;
  isInitialLoad = true;
  sortField = 'timestamp';
  sortDirection: 'asc' | 'desc' = 'desc';

  chatTypes: ChatTypeOption[] = CHAT_TYPE_OPTIONS;

  defaultChatType = ChatType.UNIFY;

  getChatTypeCount(chatType: string): number {
    if (!this.conversations || this.conversations.length === 0) {
      return 0;
    }

    if (!chatType) {
      return this.conversations.length;
    }

    return this.conversations.filter(conv => conv.chatType === chatType).length;
  }

  getChatTypeBadgeClass(chatType: string): string {
    return ChatTypeUtils.getBadgeClass(chatType);
  }


  toggleFilterPanel(): void {
    this.showFilterPanel = !this.showFilterPanel;
  }

  clearFilters(): void {
    this.filterForm.reset({
      searchStartDate: null,
      searchEndDate: null,
      chatType: '',
      search: '',
      userId: ''
    });

    this.dateRange = { start: null, end: null };
    this.searchQuery = '';
    this.selectedChatType = '';
    this.selectedUser = '';
    this.currentPage = 1;

    this.loadConversations(true);
  }

  formatDate = formatDateForDisplay;
  formatInputDate = formatDateForInput;

  private initCharts(): void {
    if (this.conversations && this.conversations.length > 0) {
      this.updateVolumeChartData(this.conversations);
      this.updateCharts();
    } else {
      this.volumeChartSeries = [{ name: 'Conversations', data: [] }];
      this.volumeChartCategories = [];
      this.donutChartSeries = [];
      this.donutChartLabels = [];
      this.donutChartColors = [];
    }
  }

  private applyFilters(): void {
    if (!this.isDataLoaded || !this.filterForm) {
      return;
    }

    this.loading = true;

    try {
      const { searchStartDate, searchEndDate, chatType, search, userId } = this.filterForm.value;
      let filteredConversations = [...this.allConversationsCache];

      if (chatType) {
        filteredConversations = filteredConversations.filter(conv => conv.chatType === chatType);
      }

      if (userId) {
        filteredConversations = filteredConversations.filter(conv => conv.userId === userId);
      }

      if (searchStartDate && searchEndDate) {
        const startDate = new Date(searchStartDate).setHours(0, 0, 0, 0);
        const endDate = new Date(searchEndDate).setHours(23, 59, 59, 999);

        filteredConversations = filteredConversations.filter(conv => {
          const convDate = new Date(conv.timestamp).getTime();
          return convDate >= startDate && convDate <= endDate;
        });
      }

      if (search?.trim()) {
        const searchLower = search.trim().toLowerCase();

        const matchingConversationIds = new Set<string>();

        this.allConversationsCache.forEach(conv => {
          const hasMatch = conv.messages.some(msg => {
            const messageContent = (msg.message || msg.content || '').toLowerCase();
            return messageContent.split(/\s+/).some(word =>
              word.includes(searchLower) || searchLower.includes(word)
            );
          });

          if (hasMatch) {
            matchingConversationIds.add(conv.id);
          }
        });

        filteredConversations = filteredConversations.filter(conv => matchingConversationIds.has(conv.id));
      }

      const sortedConversations = SortUtils.sortItems(filteredConversations, {
        field: this.sortField as SortField,
        direction: this.sortDirection as SortDirection
      });

      this.allConversations = sortedConversations;
      this.conversations = sortedConversations;
      this.updateDisplayedConversations();
      this.updateKPIs();

      if (this.currentTab === 'analytics') {
        this.updateVolumeChartData(sortedConversations);
        this.updateCharts();
      }
    } catch (error) {
      this.error = {
        message: 'Failed to apply filters',
        details: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    } finally {
      this.loading = false;
    }
  }


  private allConversationsCache: Conversation[] = [];

  async loadConversations(forceRefresh: boolean = false): Promise<void> {
    if (this.loading && !forceRefresh) return;

    this.loading = true;
    this.error = null;
    this.allConversations = [];
    this.formattedConversations = [];
    this.totalItems = 0;
    this.isDataLoaded = false;
    this.cdr.detectChanges();

    try {
      const startDate = this.dateRange.start ? formatDateForApi(this.dateRange.start) : undefined;
      const endDate = this.dateRange.end ? formatDateForApi(this.dateRange.end) : undefined;
      const { conversations: allConversations } = await this.chatAnalyticsService.fetchConversations({
        startDate,
        endDate,
        page: 1,
        pageSize: 50000
      });

      if (allConversations && allConversations.length > 0) {
        this.allConversationsCache = allConversations;
        this.isDataLoaded = true;
        this.kpis.flaggedUsers = 0;

        await this.applyFilters();
      } else {
        this.allConversations = [];
        this.formattedConversations = [];
        this.totalItems = 0;
        this.isDataLoaded = true;
      }
    } catch (error) {
      this.error = {
        message: error.message ?? 'Failed to load conversations. Please try again.',
        details: error.error?.message ?? error.statusText ?? 'Unknown error',
        status: error.status ?? 0
      };
    } finally {
      this.loading = false;
      this.cdr.detectChanges();
    }
  }

  private updateDisplayedConversations(): void {
    if (!this.isDataLoaded) return;

    const allFormatted = this.formatConversations(this.allConversations);
    this.totalItems = allFormatted.length;

    const startIndex = (this.currentPage - 1) * this.ITEMS_PER_PAGE;
    const endIndex = startIndex + this.ITEMS_PER_PAGE;

    this.formattedConversations = allFormatted.slice(startIndex, endIndex);
    this.showPagination = this.totalItems > this.ITEMS_PER_PAGE;

    this.lastUpdated = new Date();
  }

  onItemsPerPageChange(itemsPerPage: number): void {
    this.ITEMS_PER_PAGE = itemsPerPage;
    this.currentPage = 1;
    this.loadConversations();
  }

  onRefresh(): void {
    this.isInitialLoad = true;
    this.loadConversations(true);
  }

  onConversationClick(conversation: any): void {
    console.log('Conversation clicked:', conversation);
  }

  onSortChange(sortData: {field: string, direction: 'asc' | 'desc'} | string): void {
    let sortConfig: SortConfig;
    
    if (typeof sortData === 'string') {
      sortConfig = SortUtils.createSortConfigFromOption(sortData);
      this.sortBy = sortData;
    } else {
      sortConfig = { field: sortData.field as SortField, direction: sortData.direction as SortDirection };
      this.sortBy = SortUtils.getSortOptionFromConfig(sortConfig);
    }
    
    this.sortField = sortConfig.field;
    this.sortDirection = sortConfig.direction;
    this.applyFilters();
  }

  onClearFilters(): void {
    this.clearFilters();
  }

  onRetry(): void {
    this.loadConversations(true);
  }

  trackByConversationId(index: number, item: any): string {
    return item.id ?? `conv-${index}`;
  }

  onUserFilterChange(userId: string): void {
    if (this.filterForm) {
      this.filterForm.get('userId')?.setValue(userId, { emitEvent: false });
    }
  }

  onAnalyticsUserFilterChange(userId: string): void {
    this.selectedUser = userId;
    this.loadConversations();
  }

  /**
   * Handles tab changes in the UI
   * @param tab The ID of the selected tab ('conversations' | 'analytics' | 'userChatbot')
   */
  onTabChange(tab: 'conversations' | 'analytics' | 'userChatbot'): void {
    if (this.currentTab === tab) {
      return;
    }

    this.currentTab = tab;

    if (tab === 'analytics') {
      if (this.conversations && this.conversations.length > 0) {
        setTimeout(() => {
          this.initCharts();
        }, 100);
      } else {
        this.loadConversations().then(() => {
          setTimeout(() => {
            this.initCharts();
          }, 100);
        });
      }
    } else if (tab === 'conversations') {
      if (!this.isDataLoaded || this.allConversations.length === 0) {
        this.loadConversations();
      } else {
        this.updateDisplayedConversations();
      }
    }
  }

  private updateVolumeChartData(conversations: any[]): void {
    if (!conversations || conversations.length === 0) {
      this.volumeChartSeries = [{ name: 'Conversations', data: [] }];
      this.volumeChartCategories = [];
      return;
    }

    const dateCounts = new Map<string, number>();

    conversations.forEach(conv => {
      if (conv.createdAt) {
        const date = new Date(conv.createdAt);
        const dateStr = date.toISOString().split('T')[0];
        dateCounts.set(dateStr, (dateCounts.get(dateStr) || 0) + 1);
      }
    });

    const sortedDates = Array.from(dateCounts.keys()).sort();

    const seriesData = sortedDates.map(date => dateCounts.get(date) || 0);

    this.volumeChartSeries = [{
      name: 'Conversations',
      data: seriesData
    }];

    this.volumeChartCategories = sortedDates;
  }

  private updateCharts(data?: any): void {
    if (!this.conversations || this.conversations.length === 0) {
      this.donutChartSeries = [];
      this.donutChartLabels = [];
      this.donutChartColors = [];
      return;
    }

    try {
      const { series, labels } = this.processChatTypeData(this.conversations);
      this.donutChartSeries = series;
      this.donutChartLabels = labels;
      this.donutChartColors = this.getChatTypeColors(labels);

      if (data?.volumeData) {
        this.volumeChartData = data.volumeData;
      } else {
        this.updateVolumeChartData(this.conversations);
      }

    } catch (error) {
      console.error('Error updating charts:', error);
    }
  }

  ngOnDestroy(): void {
    if (this.destroy$) {
      this.destroy$.next();
      this.destroy$.complete();
    }

    this.conversations = [];
    this.donutChartSeries = [];
    this.donutChartLabels = [];
    this.volumeChartSeries = [];
    this.volumeChartCategories = [];
  }

  private isValidChatType(chatType: string | undefined): chatType is ChatType {
    if (!chatType) return false;
    return Object.values(ChatType).includes(chatType as ChatType);
  }

  private formatConversations(conversations: Conversation[]): any[] {
    const formatted = conversations
      .filter(conv => conv.messages && conv.messages.length > 0)
      .flatMap(conv => {
        const sortedMessages = [...conv.messages].sort((a, b) =>
          new Date(getMessageTime(a)).getTime() - new Date(getMessageTime(b)).getTime()
        );

        const userData = conv.userData || {};
        const userName = UserUtils.formatUserName(userData);
        const conversationPairs = [];
        let currentUserMessage = null;

        for (const msg of sortedMessages) {
          const role = (msg.role || '').toLowerCase();

          if (role === 'user' || role === 'human') {
            if (currentUserMessage) {
              conversationPairs.push({
                ...currentUserMessage,
                response: null
              });
            }
            currentUserMessage = {
              message: msg.content || msg.message || '',
              timestamp: getMessageTime(msg),
              messageId: msg.id
            };
          }
          else if ((role === 'assistant' || role === 'ai' || role === 'bot' || role === 'system' || role === 'asistant') && currentUserMessage) {
            conversationPairs.push({
              ...currentUserMessage,
              response: msg.content || msg.message || '',
              responseId: msg.id,
              responseTimestamp: getMessageTime(msg)
            });
            currentUserMessage = null;
          }
        }

        if (currentUserMessage) {
          conversationPairs.push({
            ...currentUserMessage,
            response: null
          });
        }

        return conversationPairs.map((pair, index) => ({
          id: `${conv.id}-${index}`,
          conversationId: conv.id,
          userId: conv.userId,
          userName,
          message: pair.message,
          response: pair.response,
          chatType: this.isValidChatType(conv.chatType) ? conv.chatType : this.defaultChatType,
          timestamp: formatDateForDisplay(pair.timestamp),
          createdAt: pair.timestamp,
          messageTimestamp: pair.timestamp,
          responseTimestamp: pair.responseTimestamp,
          messageId: pair.messageId,
          responseId: pair.responseId,
          hasResponse: !!pair.response,
          messages: conv.messages
        }));
      });

    return formatted.slice(0, 1000);
  }

  flaggedUserIds: string[] = [];

  onFlaggedUsersChange(flagged: string[]) {
    this.flaggedUserIds = flagged;
    this.kpis.flaggedUsers = flagged.length;
    this.cdr.detectChanges();
  }
}
