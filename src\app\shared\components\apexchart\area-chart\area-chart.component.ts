import { Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import ApexCharts from 'apexcharts';

@Component({
  selector: 'app-area-chart',
  templateUrl: './area-chart.component.html',
  styleUrls: ['./area-chart.component.scss']
})
export class AreaChartComponent implements OnInit, OnChanges, AfterViewInit, OnDestroy {
  @Input() series: ApexAxisChartSeries = [];
  @Input() categories: (string | number)[] = [];
  @Input() title: string = '';
  @Input() subtitle: string = '';
  @Input() colors: string[] = ['#7239EA'];
  @Input() height: number = 300;
  @Input() showToolbar: boolean = false;
  @Input() showLegend: boolean = true;
  @Input() legendPosition: 'left' | 'right' | 'top' | 'bottom' = 'right';
  @Input() strokeCurve: 'smooth' | 'straight' | 'stepline' = 'smooth';
  @Input() fillOpacity: number = 0.7;
  @Input() xaxisTitle: string = '';
  @Input() yaxisTitle: string = '';
  @Input() responsiveBreakpoint: number = 768;

  @ViewChild('chartContainer') private chartContainer!: ElementRef;
  private chart: ApexCharts | null = null;
  private isInitialized = false;

  constructor() { }

  ngOnInit(): void { }

  ngAfterViewInit(): void {
    this.initChart();
    this.isInitialized = true;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if ((changes['series'] || changes['categories'] || changes['colors']) && this.isInitialized) {
      this.updateChart();
    }
  }

  ngOnDestroy(): void {
    if (this.chart) {
      this.chart.destroy();
    }
  }

  private initChart(): void {
    if (!this.chartContainer?.nativeElement) {
      console.error('Chart container not found');
      return;
    }

    const options: ApexCharts.ApexOptions = this.getChartOptions();

    try {
      this.chart = new ApexCharts(this.chartContainer.nativeElement, options);
      this.chart.render();
    } catch (e) {
      console.error('Error initializing area chart:', e);
    }
  }

  private updateChart(): void {
    if (!this.chart) {
      this.initChart();
      return;
    }

    const options = this.getChartOptions();
    this.chart.updateOptions(options);
  }

  private getChartOptions(): ApexCharts.ApexOptions {
    return {
      series: this.series,
      chart: {
        type: 'area',
        height: this.height,
        width: '100%',
        toolbar: { show: this.showToolbar },
        zoom: { enabled: false },
        fontFamily: 'Inter',
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        },
        events: {
          mounted: () => {
            // Chart mounted
          },
          updated: () => {
            // Chart updated
          }
        }
      },
      colors: this.colors,
      dataLabels: { enabled: false },
      stroke: {
        curve: this.strokeCurve,
        width: 2,
        lineCap: 'round'
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.1,
          stops: [0, 80, 100]
        },
        opacity: this.fillOpacity
      },
      xaxis: {
        categories: this.categories,
        title: {
          text: this.xaxisTitle,
          style: {
            color: '#A1A5B7',
            fontSize: '12px',
            fontFamily: 'Inter'
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false },
        labels: {
          style: {
            colors: '#A1A5B7',
            fontSize: '12px',
            fontFamily: 'Inter'
          }
        },
        tooltip: {
          enabled: false
        }
      },
      yaxis: {
        title: {
          text: this.yaxisTitle,
          style: {
            color: '#A1A5B7',
            fontSize: '12px',
            fontFamily: 'Inter'
          }
        },
        labels: {
          style: {
            colors: '#A1A5B7',
            fontSize: '12px',
            fontFamily: 'Inter'
          },
          formatter: (value: number) => {
            return value % 1 === 0 ? value.toString() : value.toFixed(2);
          }
        }
      },
      grid: {
        borderColor: '#EBEDF3',
        strokeDashArray: 4,
        yaxis: { lines: { show: true } },
        padding: {
          top: 0,
          right: 10,
          bottom: 0,
          left: 10
        }
      },
      tooltip: {
        enabled: true,
        theme: 'light',
        style: {
          fontSize: '12px',
          fontFamily: 'Inter'
        },
        y: {
          formatter: (value: number) => `${value} conversations`
        }
      },
      legend: {
        position: this.legendPosition,
        horizontalAlign: 'center',
        fontSize: '12px',
        itemMargin: {
          horizontal: 8,
          vertical: 4
        },
        onItemClick: {
          toggleDataSeries: true
        },
        onItemHover: {
          highlightDataSeries: true
        }
      },
      responsive: [{
        breakpoint: this.responsiveBreakpoint,
        options: {
          chart: {
            height: 350
          },
          legend: {
            position: 'bottom',
            horizontalAlign: 'center',
            fontSize: '12px'
          }
        }
      }],
      noData: {
        text: 'Loading data...',
        align: 'center',
        verticalAlign: 'middle',
        offsetX: 0,
        offsetY: 0,
        style: {
          color: '#6c757d',
          fontSize: '14px',
          fontFamily: 'Inter'
        }
      }
    };
  }
}
