import { Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import ApexCharts from 'apexcharts';

@Component({
  selector: 'app-donut-chart',
  templateUrl: './donut-chart.component.html',
  styleUrls: ['./donut-chart.component.scss']
})
export class DonutChartComponent implements OnInit, OnChanges, AfterViewInit, OnDestroy {
  @Input() series: number[] = [];
  @Input() labels: string[] = [];
  @Input() title: string = '';
  @Input() subtitle: string = '';
  @Input() colors: string[] = [];
  @Input() height: number = 300;
  @Input() showLegend: boolean = true;
  @Input() legendPosition: 'left' | 'right' | 'top' | 'bottom' = 'right';
  @Input() responsiveBreakpoint: number = 768;

  @ViewChild('chartContainer') private chartContainer!: ElementRef;
  private chart: ApexCharts | null = null;
  private isInitialized = false;

  private readonly defaultColors: string[] = [
    '#50CD89', // Success (green)
    '#F1416C', // Danger (red)
    '#FFC700', // Warning (yellow)
    '#7239EA', // Primary (purple)
    '#009EF7', // Info (blue)
    '#FF5E5E', // Alternative red
    '#A1A5B7'  // Gray
  ];

  get effectiveColors(): string[] {
    return this.colors.length > 0 ? this.colors : this.defaultColors;
  }

  constructor() { }

  ngOnInit(): void { }

  ngAfterViewInit(): void {
    this.initChart();
    this.isInitialized = true;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if ((changes['series'] || changes['labels'] || changes['colors']) && this.isInitialized) {
      this.updateChart();
    }
  }

  ngOnDestroy(): void {
    if (this.chart) {
      this.chart.destroy();
    }
  }

  private initChart(): void {
    if (!this.chartContainer?.nativeElement) {
      console.error('Chart container not found');
      return;
    }

    const options: ApexCharts.ApexOptions = this.getChartOptions();

    try {
      this.chart = new ApexCharts(this.chartContainer.nativeElement, options);
      this.chart.render();
    } catch (e) {
      console.error('Error initializing donut chart:', e);
    }
  }

  private updateChart(): void {
    if (!this.chart) {
      this.initChart();
      return;
    }

    const options = this.getChartOptions();
    // Force a complete chart refresh to ensure colors are updated
    this.chart.updateOptions(options, true, true);
  }

  private getChartOptions(): ApexCharts.ApexOptions {
    return {
      series: this.series,
      chart: {
        type: 'donut',
        height: this.height,
        width: '100%',
        toolbar: { show: false },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        }
      },
      labels: this.labels,
      colors: this.effectiveColors.slice(0, Math.max(this.labels.length, 1)),
      dataLabels: {
        enabled: false
      },
      legend: {
        show: this.showLegend,
        position: this.legendPosition,
        horizontalAlign: 'center',
        fontSize: '12px',
        itemMargin: {
          horizontal: 8,
          vertical: 4
        },
        onItemClick: {
          toggleDataSeries: true
        },
        onItemHover: {
          highlightDataSeries: true
        },
        formatter: (legendName: string, opts: any) => {
          const series = opts.w.config.series[opts.seriesIndex];
          const total = opts.w.globals.seriesTotals.reduce((a: number, b: number) => a + b, 0);
          const percentage = total > 0 ? Math.round((series / total) * 100) : 0;
          return `${legendName}: ${series} (${percentage}%)`;
        }
      },
      responsive: [{
        breakpoint: this.responsiveBreakpoint,
        options: {
          chart: {
            height: Math.max(this.height, 350)
          },
          legend: {
            position: 'bottom',
            horizontalAlign: 'center',
            fontSize: '12px'
          }
        }
      }],
      noData: {
        text: 'No data available',
        align: 'center',
        verticalAlign: 'middle',
        style: {
          color: '#6c757d',
          fontSize: '14px',
          fontFamily: 'Inter, sans-serif'
        }
      },
      plotOptions: {
        pie: {
          donut: {
            labels: {
              show: true,
              total: {
                show: true,
                showAlways: true,
                label: 'Total',
                fontSize: '16px',
                fontWeight: 600,
                color: '#181C32',
                formatter: (w: any) => {
                  const total = w.globals.seriesTotals.reduce((a: number, b: number) => a + b, 0);
                  return total.toString();
                }
              }
            }
          }
        }
      }
    };
  }
}
