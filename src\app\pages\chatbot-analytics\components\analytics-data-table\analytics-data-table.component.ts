import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Conversation as ConversationModel, ChatTypeUtils, CHAT_TYPE_OPTIONS, ChatTypeOption } from '../../models/chat-analytics.model';
import { PaginationUtils, PaginationConfig, PaginationInfo } from '../../utils/pagination.utils';
import { UserUtils } from '../../utils/user.utils';
import { formatDateForTable } from '../../utils/date.utils';

interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'start' | 'center' | 'end';
  render?: (item: any) => string;
}

interface Conversation {
  id: string;
  timestamp: string;
  userName: string;
  userId: string;
  message: string;
  response: string;
  chatType: string;
  isFlagged?: boolean;
  messages?: Array<{
    content?: string;
    message?: string;
    response?: string;
  }>;
}

interface ErrorDetails {
  message: string;
  details?: string;
}



@Component({
  selector: 'app-analytics-data-table',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './analytics-data-table.component.html',
  styleUrls: ['./analytics-data-table.component.scss']
})
export class AnalyticsDataTableComponent implements OnChanges {
  @Input() loading = false;
  @Input() error: ErrorDetails | null = null;
  @Input() currentPage = 1;
  @Input() itemsPerPage = 10;
  @Input() totalItems = 0;
  @Input() sortField = 'timestamp';
  @Input() sortDirection: 'asc' | 'desc' = 'desc';
  @Input() conversations: Conversation[] = [];
  
  @Output() pageChange = new EventEmitter<number>();
  @Output() sortChange = new EventEmitter<{field: string, direction: 'asc' | 'desc'}>();
  @Output() conversationClick = new EventEmitter<Conversation>();
  @Output() clearFilters = new EventEmitter<void>();
  @Output() retry = new EventEmitter<void>();

  // Table columns configuration
  tableColumns: TableColumn[] = [
    {
      key: 'timestamp',
      label: 'Date/Time',
      sortable: true,
      width: '180px',
      render: (item: any) => formatDateForTable(item.timestamp)
    },
    {
      key: 'user',
      label: 'User',
      sortable: true,
      width: '200px',
      render: (item: any) => {
        return `
          <div class="d-flex align-items-center">
            <div class="symbol symbol-circle symbol-35px me-3">
              <div class="symbol-label bg-light-primary">
                <span class="text-primary fs-7 fw-bold">${(item.userName || '?').charAt(0).toUpperCase()}</span>
              </div>
            </div>
            <div class="d-flex flex-column">
              <span class="text-gray-800 text-hover-primary fw-bold text-nowrap overflow-hidden text-ellipsis" style="max-width: 150px;">
                ${item.userName || 'Unknown User'}
              </span>
              <div style="display: inline-block; margin-top: 0.25rem;">
                <span class="${ChatTypeUtils.getBadgeClass(item.chatType)} fs-8 fw-bold" style="display: inline-block; padding: 0.25em 0.5em; border-radius: 0.25rem; line-height: 1.2;">
                  ${ChatTypeUtils.getLabel(item.chatType)}
                </span>
              </div>
            </div>
          </div>
        `;
      }
    },
    { 
      key: 'message', 
      label: 'Message', 
      sortable: false 
    }
  ];

  // Chat type options for filtering
  chatTypes: ChatTypeOption[] = CHAT_TYPE_OPTIONS;

  // Formatted conversations for display
  formattedConversations: any[] = [];



  get totalPages(): number {
    return Math.ceil(this.totalItems / this.itemsPerPage);
  }

  get startIndex(): number {
    if (this.totalItems === 0) return 0;
    return (this.currentPage - 1) * this.itemsPerPage + 1;
  }

  get endIndex(): number {
    if (this.totalItems === 0) return 0;
    return Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['conversations'] && this.conversations) {
      this.formattedConversations = this.formatConversations(this.conversations);
    }
  }

  /**
   * Format conversations for display
   */
  private formatConversations(conversations: Conversation[]): any[] {
    if (!conversations || !Array.isArray(conversations)) return [];
    
    return conversations.map(conv => ({
      ...conv,
      message: conv.message || 'No message',
      response: conv.response || ''
    }));
  }

  /**
   * Handle sort column click
   */
  onSort(column: TableColumn): void {
    if (!column.sortable) return;
    
    let direction: 'asc' | 'desc' = 'desc';
    
    if (this.sortField === column.key) {
      // Toggle direction if same column
      direction = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      // Set initial direction based on column type
      direction = column.key === 'timestamp' ? 'desc' : 'asc';
    }
    
    this.sortChange.emit({ field: column.key, direction });
  }

  /**
   * Handle page change
   */
  onPageChange(page: number): void {
    const paginationInfo = PaginationUtils.getPaginationInfo({
      currentPage: this.currentPage,
      itemsPerPage: this.itemsPerPage,
      totalItems: this.totalItems
    });
    
    if (!PaginationUtils.isValidPage(page, paginationInfo.totalPages) || page === this.currentPage) {
      return;
    }
    
    this.pageChange.emit(page);
    PaginationUtils.scrollToElement();
  }

  /**
   * Handle conversation row click
   */
  onConversationClick(conversation: Conversation): void {
    this.conversationClick.emit(conversation);
  }

  /**
   * Get chat type label
   */
  getChatTypeLabel(type: string): string {
    return ChatTypeUtils.getLabel(type);
  }

  /**
   * Get chat type badge class
   */
  getChatTypeBadgeClass(chatType: string): string {
    return ChatTypeUtils.getBadgeClass(chatType);
  }

  /**
   * Clear all filters
   */
  onClearFilters(): void {
    this.clearFilters.emit();
  }

  /**
   * Retry loading data
   */
  onRetry(): void {
    this.retry.emit();
  }

  /**
   * Generate page numbers for pagination with ellipsis
   */
  getPageNumbers(): (number | string)[] {
    return PaginationUtils.getPageNumbers({
      currentPage: this.currentPage,
      itemsPerPage: this.itemsPerPage,
      totalItems: this.totalItems,
      maxVisiblePages: 5
    });
  }

  /**
   * Track by function for ngFor
   */
  trackByConversationId(index: number, item: any): string | number {
    return item?.id || index;
  }

  /**
   * Check if value is a number (for template use)
   */
  isNumber(value: any): value is number {
    return typeof value === 'number';
  }

  /**
   * Get page number from mixed value (for template use)
   */
  getPageNumber(value: any): number {
    return typeof value === 'number' ? value : 1;
  }
}
