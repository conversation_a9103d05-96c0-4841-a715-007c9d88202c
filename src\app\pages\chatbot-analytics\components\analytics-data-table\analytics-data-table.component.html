<!--begin::Card body-->
<div class="card-body pt-0">
  <!--begin::Loading-->
  <div *ngIf="loading" class="d-flex justify-content-center py-10">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
  <!--end::Loading-->
  
  <!--begin::Error-->
  <div *ngIf="error" class="alert alert-danger d-flex align-items-center p-5 mb-10">
    <i class="ki-duotone ki-cross-circle fs-2hx text-danger me-4">
      <span class="path1"></span>
      <span class="path2"></span>
    </i>
    <div class="d-flex flex-column">
      <h4 class="mb-1 text-danger">Error loading conversations</h4>
      <span>{{ error.message || 'An error occurred while loading conversations.' }}</span>
    </div>
    <button type="button" class="btn-close" (click)="error = null" aria-label="Close"></button>
  </div>
  <!--end::Error-->
  
  <!--begin::Table wrapper-->
  <div class="table-responsive" *ngIf="!loading">
    <!--begin::Table-->
    <table class="table align-middle table-row-dashed fs-6 gy-5 w-100">
      <!--begin::Table head-->
      <thead>
        <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
          <th *ngFor="let column of tableColumns" 
            [class.sorting]="column.sortable" 
            [class.sorting_asc]="sortField === column.key && sortDirection === 'asc'"
            [class.sorting_desc]="sortField === column.key && sortDirection === 'desc'"
            [style.minWidth]="column.width || 'auto'"
            [class.text-end]="column.align === 'end'"
            [class.text-center]="column.align === 'center'"
            [style.cursor]="column.sortable ? 'pointer' : 'default'"
            (click)="column.sortable && onSort(column)">
            <div class="d-flex align-items-center">
              <span class="me-2">{{ column.label }}</span>
              <i *ngIf="column.sortable" class="ki-duotone ki-arrow-down fs-2"
                [class.rotate-180]="sortField === column.key && sortDirection === 'asc'"
                [class.text-primary]="sortField === column.key">
                <span class="path1"></span>
                <span class="path2"></span>
              </i>
            </div>
          </th>
        </tr>
      </thead>
      <!--end::Table head-->
      
      <!--begin::Table body-->
      <tbody class="fw-semibold text-gray-600">
        <tr *ngFor="let conv of formattedConversations; trackBy: trackByConversationId" 
            class="cursor-pointer" 
            (click)="onConversationClick(conv)">
          <td class="text-nowrap">
            <div class="d-flex align-items-center">
              <div class="d-flex flex-column">
                {{ tableColumns[0].render(conv) }}
              </div>
            </div>
          </td>
          <td [innerHTML]="tableColumns[1].render ? tableColumns[1].render(conv) : ''"></td>
          <td>
            <div class="d-flex flex-column" style="max-width: 400px;">
              <!-- Question -->
              <div class="d-flex mb-1">
                <i class="ki-duotone ki-question fs-3 text-primary me-3 mt-1">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                <div class="text-gray-800 flex-grow-1">
                  <div class="mb-1">{{ conv.message || 'No message' }}</div>
                </div>
              </div>
              <!-- Answer -->
              <div *ngIf="conv.response !== null && conv.response !== undefined && conv.response !== ''" class="d-flex align-items-start">
                <i class="ki-duotone ki-message-text-2 fs-3 text-success me-3 mt-1">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                <div class="bg-light-success bg-opacity-10 p-3 rounded flex-grow-1 fs-7">
                  {{ conv.response }}
                </div>
              </div>
            </div>
          </td>
        </tr>
        
        <!-- Empty state row -->
        <tr *ngIf="!loading && !error && formattedConversations.length === 0" class="odd">
          <td [attr.colspan]="tableColumns.length" class="text-center py-10">
            <div class="d-flex flex-column align-items-center">
              <i class="ki-duotone ki-document-remove fs-4x text-muted mb-4">
                <span class="path1"></span>
                <span class="path2"></span>
              </i>
              <span class="text-muted fw-semibold">No conversations found</span>
              <button class="btn btn-sm btn-light-primary mt-3" (click)="clearFilters()">
                Clear filters
              </button>
            </div>
          </td>
        </tr>
      </tbody>
      <!--end::Table body-->
    </table>
    <!--end::Table-->
  </div>
  <!--end::Table wrapper-->
  
  <!--begin::Pagination-->
  <div *ngIf="!loading && formattedConversations.length > 0" class="d-flex flex-stack flex-wrap pt-5">
    <!--begin::Info-->
    <div class="fs-6 fw-semibold text-gray-700">
      Showing {{ startIndex }} to {{ endIndex }} of {{ totalItems }} entries
    </div>
    <!--end::Info-->
    
    <!--begin::Pages-->
    <ul class="pagination">
      <!--begin::Previous page-->
      <li class="page-item previous" [class.disabled]="currentPage === 1">
        <a href="javascript:;" class="page-link" (click)="$event.preventDefault(); onPageChange(currentPage - 1)">
          <i class="ki-duotone ki-arrow-left fs-2">
            <span class="path1"></span>
            <span class="path2"></span>
          </i>
        </a>
      </li>
      <!--end::Previous page-->
      
      <!-- Page numbers with ellipsis -->
      <ng-container *ngFor="let page of getPageNumbers()">
        <li *ngIf="isNumber(page)" class="page-item" [class.active]="getPageNumber(page) === currentPage">
          <a href="javascript:;" class="page-link" (click)="$event.preventDefault(); onPageChange(getPageNumber(page))">
            {{ page }}
          </a>
        </li>
        <li *ngIf="!isNumber(page)" class="page-item disabled">
          <span class="page-link">{{ page }}</span>
        </li>
      </ng-container>
      
      <!--begin::Next page-->
      <li class="page-item next" [class.disabled]="currentPage === totalPages">
        <a href="javascript:;" class="page-link" (click)="$event.preventDefault(); onPageChange(currentPage + 1)">
          <i class="ki-duotone ki-arrow-right fs-2">
            <span class="path1"></span>
            <span class="path2"></span>
          </i>
        </a>
      </li>
      <!--end::Next page-->
    </ul>
    <!--end::Pages-->
  </div>
  <!--end::Pagination-->
</div>
<!--end::Card body-->
